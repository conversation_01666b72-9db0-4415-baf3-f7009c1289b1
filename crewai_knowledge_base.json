{"metadata": {"title": "CrewAI 官方文档知识库", "version": "1.0.0", "created": "2025-01-27", "source": "https://docs.crewai.com/", "description": "基于CrewAI官方文档的结构化知识库，包含所有核心概念、技术细节和最佳实践", "language": "zh-CN", "tags": ["CrewAI", "AI Agents", "Multi-Agent", "Framework", "Documentation"]}, "knowledge_structure": {"core_concepts": ["agents", "tasks", "crews", "flows", "memory", "tools", "llms", "processes", "knowledge", "reasoning", "planning", "collaboration", "training", "testing", "cli", "event_listeners"], "advanced_features": ["mcp_integration", "observability", "enterprise_features", "custom_implementations"], "practical_guides": ["installation", "quickstart", "best_practices", "troubleshooting", "examples"]}, "knowledge_base": {"introduction": {"id": "intro_001", "title": "CrewAI 框架概述", "category": "core_concepts", "tags": ["introduction", "overview", "architecture"], "difficulty": "beginner", "definition": "CrewAI是一个轻量级、高性能的Python框架，完全独立于LangChain构建，专为创建自主AI智能体而设计", "key_features": ["独立架构：完全独立于LangChain或其他智能体框架", "双核心设计：Crews（自主协作）+ Flows（精确控制）", "高性能：针对速度和最小资源使用进行优化", "灵活定制：从高级工作流到低级智能体行为的完全自定义", "生产就绪：内置可靠性和可扩展性"], "use_cases": ["开放式研究任务", "内容生成和协作创作", "决策工作流", "API编排", "混合应用（结合Crews和Flows）"], "architecture": {"crews": "优化自主性和协作智能，创建具有特定角色、工具和目标的AI团队", "flows": "启用细粒度、事件驱动控制，支持精确任务编排并原生支持Crews"}, "code_example": "```python\nfrom crewai import Agent, Task, Crew, Process\n\n# 创建智能体\nagent = Agent(\n    role='研究分析师',\n    goal='分析复杂数据并提供洞察',\n    backstory='经验丰富的数据分析专家'\n)\n\n# 创建任务\ntask = Task(\n    description='分析最新AI发展趋势',\n    expected_output='包含关键趋势的分析报告',\n    agent=agent\n)\n\n# 创建团队\ncrew = Crew(\n    agents=[agent],\n    tasks=[task],\n    process=Process.sequential\n)\n\n# 执行\nresult = crew.kickoff()\n```", "related_concepts": ["agents", "tasks", "crews", "flows"], "best_practices": ["根据任务复杂度选择Crews或Flows", "使用YAML配置提高可维护性", "启用详细日志进行调试", "合理设置资源限制"]}, "agents": {"id": "agents_001", "title": "Agents（智能体）", "category": "core_concepts", "tags": ["agents", "ai", "autonomous", "roles"], "difficulty": "beginner", "definition": "Agent是CrewAI框架中的自主单元，能够执行特定任务、基于角色和目标做出决策、使用工具完成目标、与其他智能体通信协作、维护交互记忆并在允许时委派任务", "core_attributes": {"role": {"type": "str", "required": true, "description": "定义智能体在团队中的功能和专业知识", "example": "高级数据科学家"}, "goal": {"type": "str", "required": true, "description": "指导智能体决策的个人目标", "example": "分析复杂数据集并提供可操作的洞察"}, "backstory": {"type": "str", "required": true, "description": "为智能体提供背景和个性，丰富交互", "example": "拥有10年数据科学和机器学习经验，擅长在复杂数据集中发现模式"}, "llm": {"type": "Union[str, LLM, Any]", "required": false, "description": "驱动智能体的语言模型", "default": "gpt-4o-mini"}}, "advanced_features": {"reasoning": {"description": "启用智能体在执行任务前进行反思和规划", "parameter": "reasoning=True", "max_attempts": "max_reasoning_attempts=3"}, "memory": {"description": "维护交互历史和上下文", "parameter": "memory=True"}, "code_execution": {"description": "允许智能体执行代码", "parameter": "allow_code_execution=True", "modes": ["safe", "unsafe"]}, "multimodal": {"description": "支持多模态能力处理文本和视觉内容", "parameter": "multimodal=True"}}, "code_examples": {"basic_agent": "```python\nfrom crewai import Agent, LLM\n\n# 基础智能体创建\nagent = Agent(\n    role='数据分析师',\n    goal='分析数据并提供洞察',\n    backstory='经验丰富的数据分析专家',\n    verbose=True\n)\n```", "advanced_agent": "```python\n# 高级智能体配置\nlocal_llm = LLM(\n    model='ollama/qwen2.5:14b',\n    base_url='http://localhost:11434'\n)\n\nadvanced_agent = Agent(\n    role='高级AI研究员',\n    goal='进行前沿AI研究',\n    backstory='AI领域的资深研究专家',\n    llm=local_llm,\n    reasoning=True,\n    max_reasoning_attempts=3,\n    memory=True,\n    allow_code_execution=True,\n    multimodal=True,\n    tools=[search_tool, analysis_tool],\n    max_iter=20,\n    max_execution_time=300\n)\n```"}, "best_practices": ["为每个智能体定义清晰、具体的角色", "确保目标与角色一致", "提供丰富的背景故事增强个性", "根据任务复杂度选择合适的LLM", "合理设置执行限制避免无限循环", "使用推理功能处理复杂任务", "启用记忆功能保持上下文连续性"], "common_issues": [{"issue": "智能体响应不符合预期", "solution": "检查角色定义是否清晰，目标是否具体"}, {"issue": "执行时间过长", "solution": "设置max_execution_time和max_iter限制"}, {"issue": "上下文丢失", "solution": "启用memory=True并配置合适的嵌入模型"}]}, "tasks": {"id": "tasks_001", "title": "Tasks（任务）", "category": "core_concepts", "tags": ["tasks", "execution", "workflow", "output"], "difficulty": "beginner", "definition": "Task是CrewAI框架中由Agent完成的具体任务分配，提供执行所需的所有必要细节，如描述、负责的智能体、所需工具等，支持从简单到复杂的各种操作", "core_attributes": {"description": {"type": "str", "required": true, "description": "任务内容的清晰、简洁描述", "example": "对{topic}进行深入研究，确保找到任何有趣和相关的信息"}, "expected_output": {"type": "str", "required": true, "description": "任务完成后预期输出的详细描述", "example": "包含{topic}最相关信息的10个要点列表"}, "agent": {"type": "Optional[BaseAgent]", "required": false, "description": "负责执行任务的智能体"}, "tools": {"type": "List[BaseTool]", "required": false, "description": "智能体执行此任务时限制使用的工具/资源"}}, "execution_flow": {"sequential": "任务按定义顺序执行", "hierarchical": "根据智能体角色和专业知识分配任务"}, "output_formats": {"raw": "默认原始文本输出", "json": "结构化JSON输出，使用output_json参数", "pydantic": "Pydantic模型输出，使用output_pydantic参数", "markdown": "Markdown格式输出，使用markdown=True"}, "advanced_features": {"context": {"description": "使用其他任务的输出作为上下文", "parameter": "context=[task1, task2]"}, "async_execution": {"description": "异步执行任务", "parameter": "async_execution=True"}, "human_input": {"description": "需要人工审核最终答案", "parameter": "human_input=True"}, "guardrails": {"description": "验证任务输出的函数", "parameter": "guardrail=validation_function"}, "callbacks": {"description": "任务完成后执行的回调函数", "parameter": "callback=callback_function"}}, "code_examples": {"basic_task": "```python\nfrom crewai import Task\n\n# 基础任务创建\ntask = Task(\n    description='对AI发展趋势进行深入研究',\n    expected_output='包含最新AI发展的10个要点列表',\n    agent=research_agent\n)\n```", "structured_output": "```python\nfrom pydantic import BaseModel\n\nclass BlogPost(BaseModel):\n    title: str\n    content: str\n    tags: List[str]\n\n# 结构化输出任务\nstructured_task = Task(\n    description='创建关于AI的博客文章',\n    expected_output='包含标题、内容和标签的博客文章',\n    agent=writer_agent,\n    output_pydantic=BlogPost\n)\n```", "task_with_context": "```python\n# 依赖其他任务的任务\nanalysis_task = Task(\n    description='分析研究结果并识别关键趋势',\n    expected_output='AI趋势分析报告',\n    agent=analyst_agent,\n    context=[research_task]  # 等待research_task完成\n)\n```"}, "best_practices": ["编写清晰、具体的任务描述", "明确定义预期输出格式", "合理使用任务依赖关系", "为复杂任务启用异步执行", "使用guardrails验证输出质量", "设置适当的回调函数监控进度"]}, "crews": {"id": "crews_001", "title": "Crews（团队）", "category": "core_concepts", "tags": ["crews", "collaboration", "workflow", "orchestration"], "difficulty": "intermediate", "definition": "Crew代表CrewAI框架中协作工作的智能体团队，定义任务执行策略、智能体协作方式和整体工作流程", "core_attributes": {"agents": {"type": "List[BaseAgent]", "required": true, "description": "团队中的智能体列表"}, "tasks": {"type": "List[Task]", "required": true, "description": "分配给团队的任务列表"}, "process": {"type": "Process", "required": false, "description": "团队遵循的流程（顺序或层次）", "default": "Process.sequential", "options": ["sequential", "hierarchical"]}, "memory": {"type": "bool", "required": false, "description": "启用记忆存储（短期、长期、实体记忆）", "default": false}, "verbose": {"type": "bool", "required": false, "description": "执行期间的详细日志级别", "default": false}}, "execution_processes": {"sequential": {"description": "任务按顺序逐一执行", "use_case": "线性工作流，任务间有明确依赖关系"}, "hierarchical": {"description": "管理者智能体协调团队，委派任务并验证结果", "requirements": "需要manager_llm或manager_agent", "use_case": "复杂项目需要统一协调和质量控制"}}, "advanced_features": {"memory_system": {"short_term": "使用ChromaDB+RAG存储当前上下文", "long_term": "使用SQLite3存储跨会话任务结果", "entity": "使用RAG跟踪实体（人、地点、概念）"}, "embedder_config": {"openai": "默认嵌入提供商", "ollama": "本地嵌入模型，隐私友好", "google": "Google AI嵌入", "azure": "Azure OpenAI嵌入"}, "kickoff_methods": {"kickoff": "标准同步执行", "kickoff_async": "异步执行", "kickoff_for_each": "为每个输入项顺序执行", "kickoff_for_each_async": "为每个输入项并发执行"}}, "code_examples": {"basic_crew": "```python\nfrom crewai import Agent, Task, Crew, Process\n\n# 创建基础团队\ncrew = Crew(\n    agents=[researcher, analyst, writer],\n    tasks=[research_task, analysis_task, writing_task],\n    process=Process.sequential,\n    verbose=True\n)\n\n# 执行团队任务\nresult = crew.kickoff()\n```", "hierarchical_crew": "```python\n# 层次化团队（需要管理者）\nmanager_llm = LLM(model='gpt-4')\n\nhierarchical_crew = Crew(\n    agents=[researcher, analyst, writer],\n    tasks=[research_task, analysis_task, writing_task],\n    process=Process.hierarchical,\n    manager_llm=manager_llm,\n    verbose=True\n)\n```", "crew_with_memory": "```python\n# 启用记忆的团队\nmemory_crew = Crew(\n    agents=[agent1, agent2],\n    tasks=[task1, task2],\n    memory=True,\n    embedder={\n        'provider': 'ollama',\n        'config': {'model': 'mxbai-embed-large'}\n    },\n    verbose=True\n)\n```"}, "best_practices": ["根据任务复杂度选择合适的执行流程", "为层次化流程配置合适的管理者LLM", "启用记忆功能提高团队协作效果", "使用本地嵌入模型保护数据隐私", "合理设置详细日志级别便于调试", "为不同场景选择合适的kickoff方法"]}, "flows": {"id": "flows_001", "title": "Flows（流程）", "category": "core_concepts", "tags": ["flows", "event-driven", "control", "orchestration"], "difficulty": "advanced", "definition": "Flow是CrewAI的事件驱动架构，提供精确的任务编排和流程控制，支持复杂的条件逻辑和状态管理", "core_decorators": {"@start()": {"description": "标记流程的起始点", "usage": "每个Flow必须有且仅有一个@start()方法"}, "@listen(method)": {"description": "监听指定方法的完成事件", "usage": "创建方法间的依赖关系"}, "@router(method)": {"description": "基于条件进行路由决策", "usage": "返回字符串决定下一步执行路径"}, "@and_(method1, method2)": {"description": "等待多个方法都完成", "usage": "实现AND逻辑的依赖关系"}, "@or_(method1, method2)": {"description": "等待任一方法完成", "usage": "实现OR逻辑的依赖关系"}}, "state_management": {"description": "Flow支持状态持久化和管理", "features": ["自动状态保存到SQLite数据库", "重启后状态自动恢复", "支持自定义状态模型", "状态在方法间共享"]}, "code_examples": {"basic_flow": "```python\nfrom crewai.flow.flow import Flow, start, listen, router\nfrom pydantic import BaseModel\n\nclass AnalysisState(BaseModel):\n    data: str = ''\n    result: dict = {}\n    confidence: float = 0.0\n\nclass AnalysisFlow(Flow[AnalysisState]):\n    @start()\n    def initialize(self):\n        self.state.data = 'input_data'\n        return {'status': 'initialized'}\n    \n    @listen(initialize)\n    def process_data(self, init_result):\n        self.state.result = {'processed': True}\n        self.state.confidence = 0.85\n        return self.state.result\n    \n    @router(process_data)\n    def decide_next_step(self):\n        if self.state.confidence > 0.8:\n            return 'high_confidence_path'\n        return 'low_confidence_path'\n    \n    @listen('high_confidence_path')\n    def execute_high_confidence(self):\n        return 'High confidence execution completed'\n\n# 执行流程\nflow = AnalysisFlow()\nresult = flow.kickoff()\n```"}, "best_practices": ["使用@start()标记唯一的流程起始点", "合理使用@router()实现条件分支", "利用状态管理维护流程上下文", "使用@and_()和@or_()处理复杂依赖", "启用状态持久化保证流程可恢复性"]}, "knowledge": {"id": "knowledge_001", "title": "Knowledge（知识库）", "category": "core_concepts", "tags": ["knowledge", "rag", "embeddings", "sources"], "difficulty": "intermediate", "definition": "Knowledge是CrewAI的强大系统，允许AI智能体在执行任务时访问和利用外部信息源，如给智能体提供可咨询的参考图书馆", "key_benefits": ["使用领域特定信息增强智能体", "用真实世界数据支持决策", "在对话中维护上下文", "在事实信息中建立响应基础"], "supported_sources": {"text_sources": ["原始字符串（StringKnowledgeSource）", "文本文件（TextFileKnowledgeSource）", "PDF文档（PDFKnowledgeSource）"], "structured_data": ["CSV文件（CSVKnowledgeSource）", "Excel电子表格（ExcelKnowledgeSource）", "JSON文档（JSONKnowledgeSource）"], "web_content": ["网页内容（CrewDoclingSource）", "自定义知识源（BaseKnowledgeSource）"]}, "knowledge_levels": {"agent_level": {"description": "智能体级别的独立知识", "use_case": "角色特定信息", "storage": "按智能体角色名称存储"}, "crew_level": {"description": "团队级别的共享知识", "use_case": "所有智能体需要的共享信息", "storage": "存储在'crew'集合中"}}, "storage_system": {"technology": "ChromaDB向量存储", "locations": {"macos": "~/Library/Application Support/CrewAI/{project}/knowledge/", "linux": "~/.local/share/CrewAI/{project}/knowledge/", "windows": "C:\\Users\\<USER>\\AppData\\Local\\CrewAI\\{project}\\knowledge\\"}, "customization": "通过CREWAI_STORAGE_DIR环境变量自定义"}, "code_examples": {"basic_knowledge": "```python\nfrom crewai import Agent, Task, Crew\nfrom crewai.knowledge.source.string_knowledge_source import StringKnowledgeSource\n\n# 创建知识源\ncontent = '用户名是John，30岁，住在旧金山。'\nstring_source = StringKnowledgeSource(content=content)\n\n# 创建带知识的智能体\nagent = Agent(\n    role='用户信息专家',\n    goal='了解用户的一切信息',\n    backstory='擅长理解人和他们偏好的专家',\n    verbose=True\n)\n\n# 创建带知识的团队\ncrew = Crew(\n    agents=[agent],\n    tasks=[task],\n    knowledge_sources=[string_source]  # 在这里启用知识\n)\n\nresult = crew.kickoff(inputs={'question': 'John住在哪个城市，多少岁？'})\n```", "custom_embedder": "```python\n# 使用本地嵌入模型\ncrew = Crew(\n    agents=[agent],\n    tasks=[tasks],\n    knowledge_sources=[knowledge_source],\n    embedder={\n        'provider': 'ollama',\n        'config': {'model': 'mxbai-embed-large'}\n    }\n)\n```"}, "best_practices": ["将文件放在项目根目录的knowledge文件夹中", "根据内容类型调整块大小", "为智能体特定信息使用智能体级知识", "为共享信息使用团队级知识", "选择与LLM设置匹配的嵌入提供商", "在生产环境中设置CREWAI_STORAGE_DIR", "使用一致的集合命名保持智能体角色描述性"]}, "tools": {"id": "tools_001", "title": "Tools（工具）", "category": "core_concepts", "tags": ["tools", "capabilities", "integration", "custom"], "difficulty": "intermediate", "definition": "CrewAI工具是智能体可以利用执行各种操作的技能或功能，包括网络搜索、数据分析、协作和任务委派等能力", "key_characteristics": ["实用性：为网络搜索、数据分析、内容生成和智能体协作等任务而设计", "集成性：通过无缝集成工具到工作流程中提升智能体能力", "可定制性：提供开发自定义工具或使用现有工具的灵活性", "错误处理：包含强大的错误处理机制确保平稳运行", "缓存机制：具有智能缓存优化性能并减少冗余操作", "异步支持：处理同步和异步工具，支持非阻塞操作"], "tool_categories": {"file_document": ["DirectoryReadTool - 读取目录结构", "FileReadTool - 读取文件内容", "PDFSearchTool - PDF文档搜索", "DOCXSearchTool - Word文档搜索", "TXTSearchTool - 文本文件搜索"], "web_scraping": ["ScrapeWebsiteTool - 网站抓取", "WebsiteSearchTool - 网站内容搜索", "FirecrawlScrapeWebsiteTool - Firecrawl网页抓取", "BrowserbaseLoadTool - 浏览器数据提取"], "search_research": ["SerperDevTool - 开发搜索工具", "EXASearchTool - 详尽搜索工具", "GithubSearchTool - GitHub仓库搜索"], "data_analysis": ["CSVSearchTool - CSV文件搜索", "JSONSearchTool - JSON文件搜索", "XMLSearchTool - XML文件搜索", "PGSearchTool - PostgreSQL数据库搜索"]}, "creation_methods": {"subclassing": {"description": "继承BaseTool类创建自定义工具", "example": "class MyCustomTool(BaseTool):"}, "decorator": {"description": "使用@tool装饰器创建工具", "example": "@tool('工具名称')"}}, "code_examples": {"basic_tool_usage": "```python\nfrom crewai import Agent, Task, Crew\nfrom crewai_tools import SerperDevTool, FileReadTool\n\n# 实例化工具\nsearch_tool = SerperDevTool()\nfile_tool = FileReadTool()\n\n# 创建带工具的智能体\nresearcher = Agent(\n    role='市场研究分析师',\n    goal='提供AI行业的最新市场分析',\n    backstory='具有敏锐市场趋势洞察力的专家分析师',\n    tools=[search_tool],\n    verbose=True\n)\n\nwriter = Agent(\n    role='内容写手',\n    goal='撰写关于AI行业的引人入胜的博客文章',\n    backstory='对技术充满热情的熟练写手',\n    tools=[file_tool],\n    verbose=True\n)\n```", "custom_tool": "```python\nfrom crewai.tools import BaseTool\nfrom pydantic import BaseModel, Field\n\nclass MyToolInput(BaseModel):\n    argument: str = Field(..., description='参数描述')\n\nclass MyCustomTool(BaseTool):\n    name: str = '我的工具名称'\n    description: str = '这个工具的功能描述，对有效使用至关重要'\n    args_schema: Type[BaseModel] = MyToolInput\n    \n    def _run(self, argument: str) -> str:\n        # 工具逻辑\n        return '工具执行结果'\n```", "async_tool": "```python\nfrom crewai.tools import tool\nimport asyncio\n\n@tool('异步数据获取')\nasync def fetch_data_async(query: str) -> str:\n    '''异步获取基于查询的数据'''\n    await asyncio.sleep(1)  # 模拟异步操作\n    return f'为{query}检索的数据'\n```"}, "best_practices": ["为工具提供清晰、描述性的名称和说明", "实现强大的错误处理机制", "利用缓存机制优化性能", "为长时间运行的操作使用异步工具", "根据智能体需求选择合适的工具", "自定义缓存函数实现精细控制"]}, "practical_applications": {"id": "applications_001", "title": "实战应用场景", "category": "practical_guides", "tags": ["applications", "use-cases", "examples", "templates"], "difficulty": "advanced", "scenarios": {"content_production": {"title": "企业级内容生产流水线", "description": "多智能体协作的内容创作系统", "agents": ["研究专家", "内容创作者", "编辑总监", "SEO专家"], "workflow": "研究→创作→编辑→SEO优化", "technologies": ["CrewAI", "SerperDevTool", "WikipediaTools"]}, "customer_service": {"title": "智能客服系统", "description": "基于知识库的自动客服解决方案", "agents": ["问题分类专家", "技术支持专家", "销售顾问"], "workflow": "问题接收→分类→专家处理→回复发送", "technologies": ["CrewAI", "Knowledge Sources", "Flow控制"]}, "data_analysis": {"title": "数据分析流水线", "description": "自动化数据收集、分析和报告生成", "agents": ["数据收集员", "数据分析师", "报告生成器"], "workflow": "数据收集→清洗分析→可视化→报告生成", "technologies": ["CrewAI", "CSVSearchTool", "数据分析工具"]}}}, "best_practices_summary": {"id": "best_practices_001", "title": "CrewAI最佳实践总结", "category": "practical_guides", "tags": ["best-practices", "guidelines", "optimization", "production"], "difficulty": "advanced", "categories": {"agent_design": ["为每个智能体定义清晰、具体的角色", "确保目标与角色一致", "提供丰富的背景故事增强个性", "根据任务复杂度选择合适的LLM", "合理设置执行限制避免无限循环"], "task_management": ["编写清晰、具体的任务描述", "明确定义预期输出格式", "合理使用任务依赖关系", "为复杂任务启用异步执行", "使用guardrails验证输出质量"], "crew_orchestration": ["根据任务复杂度选择合适的执行流程", "为层次化流程配置合适的管理者LLM", "启用记忆功能提高团队协作效果", "使用本地嵌入模型保护数据隐私", "合理设置详细日志级别便于调试"], "production_deployment": ["设置CREWAI_STORAGE_DIR到已知位置", "选择明确的嵌入提供商匹配LLM设置", "监控知识存储大小随文档增长", "按域或目的组织知识源", "在备份和部署策略中包含知识目录"]}}}}