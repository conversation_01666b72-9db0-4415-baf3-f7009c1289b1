# CrewAI 快速参考卡片

## 🚀 核心组件速查

### Agent（智能体）
```python
from crewai import Agent, LLM

agent = Agent(
    role="数据分析师",                    # 角色定义
    goal="分析数据并提供洞察",             # 目标
    backstory="经验丰富的数据专家",        # 背景故事
    llm=LLM(model="ollama/qwen2.5:14b"),  # 本地模型
    reasoning=True,                       # 启用推理
    memory=True,                         # 启用记忆
    tools=[search_tool, analysis_tool],  # 工具列表
    verbose=True                         # 详细日志
)
```

### Task（任务）
```python
from crewai import Task

task = Task(
    description="分析最新AI发展趋势",      # 任务描述
    expected_output="包含关键趋势的报告",  # 预期输出
    agent=agent,                         # 执行智能体
    tools=[specific_tool],               # 任务专用工具
    context=[previous_task],             # 依赖任务
    async_execution=True,                # 异步执行
    output_file="report.md"              # 输出文件
)
```

### Crew（团队）
```python
from crewai import Crew, Process

crew = Crew(
    agents=[researcher, analyst, writer], # 智能体列表
    tasks=[research, analyze, write],     # 任务列表
    process=Process.sequential,           # 执行流程
    memory=True,                         # 启用记忆
    verbose=True,                        # 详细日志
    embedder={                           # 嵌入配置
        "provider": "ollama",
        "config": {"model": "mxbai-embed-large"}
    }
)

result = crew.kickoff()                  # 执行团队任务
```

### Flow（流程）
```python
from crewai.flow.flow import Flow, start, listen, router

class AnalysisFlow(Flow):
    @start()
    def initialize(self):
        return {"status": "started"}
    
    @listen(initialize)
    def process_data(self, init_result):
        return {"data": "processed"}
    
    @router(process_data)
    def decide_next_step(self):
        return "high_confidence_path"
    
    @listen("high_confidence_path")
    def execute_plan(self):
        return "completed"

flow = AnalysisFlow()
result = flow.kickoff()
```

## 🛠️ 常用工具速查

### 文件操作工具
```python
from crewai_tools import (
    FileReadTool,           # 读取文件
    DirectoryReadTool,      # 读取目录
    PDFSearchTool,          # PDF搜索
    CSVSearchTool           # CSV搜索
)
```

### 网络工具
```python
from crewai_tools import (
    SerperDevTool,          # 网络搜索
    ScrapeWebsiteTool,      # 网站抓取
    WebsiteSearchTool       # 网站搜索
)
```

### 自定义工具
```python
from crewai.tools import BaseTool, tool

# 方法1：继承BaseTool
class CustomTool(BaseTool):
    name: str = "自定义工具"
    description: str = "工具描述"
    
    def _run(self, query: str) -> str:
        return "工具结果"

# 方法2：使用装饰器
@tool("工具名称")
def my_tool(input: str) -> str:
    """工具功能描述"""
    return "处理结果"
```

## 📚 知识库集成

### 基础知识源
```python
from crewai.knowledge.source.string_knowledge_source import StringKnowledgeSource

knowledge = StringKnowledgeSource(
    content="知识内容文本"
)

# 智能体级知识
agent = Agent(
    role="专家",
    knowledge_sources=[knowledge]
)

# 团队级知识
crew = Crew(
    agents=[agent],
    tasks=[task],
    knowledge_sources=[knowledge]
)
```

### 文件知识源
```python
from crewai.knowledge.source.pdf_knowledge_source import PDFKnowledgeSource

pdf_knowledge = PDFKnowledgeSource(
    file_paths=["document.pdf"]
)
```

## 🧠 记忆系统

### 启用记忆
```python
# 团队级记忆
crew = Crew(
    agents=[agent],
    tasks=[task],
    memory=True,                    # 启用记忆
    embedder={                      # 嵌入配置
        "provider": "ollama",
        "config": {"model": "mxbai-embed-large"}
    }
)

# 重置记忆
crew.reset_memories()
```

## ⚙️ 本地模型配置

### Ollama集成
```python
from crewai import LLM

# 本地LLM
local_llm = LLM(
    model="ollama/qwen2.5:14b",
    base_url="http://localhost:11434"
)

# 本地嵌入
embedder = {
    "provider": "ollama",
    "config": {"model": "mxbai-embed-large"}
}
```

## 🔧 常用配置

### 环境变量
```bash
# 自定义存储位置
export CREWAI_STORAGE_DIR="./my_project_storage"

# API密钥
export OPENAI_API_KEY="your-key"
export SERPER_API_KEY="your-key"
```

### 执行方法
```python
# 同步执行
result = crew.kickoff()

# 异步执行
result = await crew.kickoff_async()

# 批量执行
inputs = [{"topic": "AI"}, {"topic": "ML"}]
results = crew.kickoff_for_each(inputs=inputs)

# 异步批量执行
results = await crew.kickoff_for_each_async(inputs=inputs)
```

## 🐛 调试技巧

### 启用详细日志
```python
agent = Agent(verbose=True)
crew = Crew(verbose=True)
```

### 检查存储位置
```python
from crewai.utilities.paths import db_storage_path
print(f"存储位置: {db_storage_path()}")
```

### 重置系统
```bash
# 重置记忆
crewai reset-memories

# 重置知识
crewai reset-memories --knowledge
```

## 📊 性能优化

### 资源限制
```python
agent = Agent(
    max_iter=20,                # 最大迭代次数
    max_execution_time=300,     # 最大执行时间（秒）
    max_retry_limit=3           # 最大重试次数
)

crew = Crew(
    max_rpm=10                  # API调用频率限制
)
```

### 缓存配置
```python
crew = Crew(
    cache=True                  # 启用缓存
)
```

---

*快速参考 - 基于CrewAI官方文档v1.0.0*
